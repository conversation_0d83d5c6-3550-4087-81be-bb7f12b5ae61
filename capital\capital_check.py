import tkinter as tk
from functools import partial
import pandas as pd


root=tk.Tk()
s1=tk.StringVar()
c1=tk.StringVar()

#result
def call_result(label_result,s1,c1,data):
    a=(s1.get())
    b=(c1.get())
    a.lower()
    b.lower()
    l=data.values.tolist()
    try:
        for i in l:
            if i[0]==a:
                if i[1]==b:
                    label_result.config(text="Correct")
                else:
                    label_result.config(text="Wrong")
    except:
        label_result.config(text="Error")
        
                
        
#label
l1=tk.Label(root,text="Enter State")
l1.grid(row=0,column=0)
l2=tk.Label(root,text="Enter Capital")
l2.grid(row=1,column=0)
l3=tk.Label(root)
l3.grid(row=1,column=1)

#entry
e1=tk.Entry(root,textvariable=s1)
e2=tk.Entry(root,textvariable=c1)
e1.grid(row=0,column=1)
e2.grid(row=1,column=1)

#csv
df=pd.read_csv("cap.csv")
data=pd.DataFrame(df)

#button
call_result = partial(call_result,l3,s1,c1,data)
but=tk.Button(text="Check",command=call_result)
but.grid(row=2,column=2)


root.mainloop()
