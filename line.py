import matplotlib.pyplot as plt
import numpy as np
x=[]
y=[]
size=int(input("Enter number of inputs: "))
for i in range(0,size):
    x=x+[int(input("Enter x: "))]
    y=y+[int(input("Enter y: "))]
l=len(x)
m=[]
res=[]
for i in range(0,l):
    for j in range(0,101):
        for k in range(0,101):
            if(((x[i]*j)+k)==y[i]):
                m=m+[[j,k]]
for i in range(0,len(m)):
    count=0
    for j in range(i+1,len(m)):
        if(m[i]==m[j]):
            count+=1
    res=res+[[count,i]]
for i in range(0,len(res)):
    for j in range(0,len(res)-i-1):
        if(res[j][0]>res[j+1][0]):
            temp=res[j]
            res[j]=res[j+1]
            res[j+1]=temp
index=res[len(res)-1][1]
print("y=mx+c")
print("m=",m[index][0])
print("c=",m[index][1])
xpoints = np.array(x)
ypoints = np.array(y)
plt.plot(xpoints, ypoints)
plt.show()
