import tkinter as tk
from tkinter import ttk
from PIL import Image,ImageTk,ImageDraw

class Meter(ttk.Label):

    def __init__(self,parent,**kwargs):
        self.font="helvetica 12 bold"
        self.foreground="#FFFFFF"
        self.background="#000000"
        self.im=Image.new("RGBA",(1000,1000))
        self.hollowcolor="#e0e0e0"
        self.size=200
        self.textvariable=tk.StringVar()
        self.arcvariable=tk.IntVar(value="text")
        self.arc=None
        self.indicator="#FF0000"
        self.arcvariable.trace_add("write",self.update_arcvariable)

        self.setup()

        super().__init__(parent,image=self.arc,compound="center",style="Meter.TLabel",
                         textvariable=self.textvariable,**kwargs)

    def setup(self):
        style=ttk.Style()
        style.configure("Meter.TLabel",font=self.font,foreground=self.foreground)
        if self.background:
            style.configure("Meter.TLable",background=self.background)
        draw=ImageDraw.Draw(self.im)
        draw.arc((0,0,990,990),0,360,self.hollowcolor,100)
        self.arc=ImageTk.PhotoImage(self.im.resize((self.size,self.size),Image.LANCZOS))

    def update_arcvariable(self,*args):
        angle=int(float(self.arcvariable.get()))+90
        self.im=Image.new("RGBA",(1000,1000))
        draw=ImageDraw.Draw(self.im)
        draw.arc((0,0,990,990),0,360,self.hollowcolor,100)
        draw.arc((0,0,990,990),90,angle,self.indicator,100)
        self.arc=ImageTk.PhotoImage(self.im.resize((self.size,self.size),Image.LANCZOS))
        self.configure(image=self.arc)


if __name__=="__main__":
    root=tk.Tk()
    meter=Meter(root,padding=20)
    meter.pack()

    ttk.Scale(root,from_=0,to=360,variable=meter.arcvariable).pack(fill="x",padx=10,pady=10)
    meter.arcvariable.trace_add("write",lambda *args,g=meter: g.textvariable.set(f'{g.arcvariable.get()}'))

    
    root.mainloop()
